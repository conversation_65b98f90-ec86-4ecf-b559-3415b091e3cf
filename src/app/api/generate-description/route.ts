import { NextResponse } from 'next/server';
import OpenAI from 'openai';

const apiKey = process.env.OPENAI_API_KEY;
const fallbackApiKey = process.env.OPENAI_FALLBACK_API_KEY;
const mockMode = process.env.MOCK_OPENAI === 'true';

let openai: OpenAI | null = null;
if (apiKey && !mockMode) {
  openai = new OpenAI({ apiKey });
} else if (fallbackApiKey && !mockMode) {
  openai = new OpenAI({ apiKey: fallbackApiKey });
}

interface ProductInfo {
  name: string;
  category?: string;
  features?: string[];
  keywords?: string[];
  targetAudience?: string;
  additionalInfo?: string;
}

interface SeoContent {
  shortDescription: string;
  slug: string;
  wooCommerceMainDescription: string;
  wooCommerceShortDescription: string;
}

// Função para validar limites de caracteres SEO
const validateSeoLimits = (content: SeoContent): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Validar meta description (140-160 caracteres)
  if (content.shortDescription.length < 140 || content.shortDescription.length > 160) {
    errors.push(`Meta description deve ter entre 140-160 caracteres (atual: ${content.shortDescription.length})`);
  }

  // Validar título SEO (50-60 caracteres recomendado para o slug)
  if (content.slug.length > 60) {
    errors.push(`Slug muito longo (atual: ${content.slug.length} caracteres)`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Função para normalizar o slug
const generateSlug = (text: string): string => {
  const a = 'àáâäæãåāăąçćčđďèéêëēėęěğǵḧîïíīįìłḿñńǹňôöòóœøōõőṕŕřßśšşșťțûüùúūǘůűųẃẍÿýžźż·/_,:;';
  const b = 'aaaaaaaaaacccddeeeeeeeegghiiiiiilmnnnnoooooooooprrssssssttuuuuuuuuuwxyyzzz------';
  const p = new RegExp(a.split('').join('|'), 'g');

  return text.toString().toLowerCase()
    .replace(/\s+/g, '-') // Substituir espaços por -
    .replace(p, c => b.charAt(a.indexOf(c))) // Substituir caracteres especiais
    .replace(/&/g, '-and-') // Substituir & por '-and-'
    .replace(/[^\w\-]+/g, '') // Remover caracteres inválidos
    .replace(/\-\-+/g, '-') // Substituir múltiplos - por um único -
    .replace(/^-+/, '') // Remover - do início
    .replace(/-+$/, ''); // Remover - do fim
};

// Automatic error correction functions
function standardizeTitle(title: string): string {
  if (!title) return '';

  // Remove extra spaces and normalize
  let standardized = title.trim().replace(/\s+/g, ' ');

  // Apply proper case (capitalize first letter of each word, except articles/prepositions)
  const lowercaseWords = ['de', 'da', 'do', 'das', 'dos', 'e', 'em', 'para', 'com', 'por', 'a', 'o', 'as', 'os'];

  standardized = standardized.toLowerCase().split(' ').map((word, index) => {
    // Always capitalize first word
    if (index === 0) {
      return word.charAt(0).toUpperCase() + word.slice(1);
    }
    // Don't capitalize articles/prepositions unless they're the first word
    if (lowercaseWords.includes(word)) {
      return word;
    }
    // Capitalize other words
    return word.charAt(0).toUpperCase() + word.slice(1);
  }).join(' ');

  // Remove unnecessary punctuation at the end
  standardized = standardized.replace(/[.,;:!?]+$/, '');

  return standardized;
}

function correctSpelling(text: string): string {
  if (!text) return '';

  // Common Portuguese spelling corrections
  const corrections: { [key: string]: string } = {
    // Common typos
    'qualidadde': 'qualidade',
    'funcionalidadde': 'funcionalidade',
    'resistênte': 'resistente',
    'duravél': 'durável',
    'portátil': 'portátil',
    'ergonómico': 'ergonómico',
    'económico': 'económico',
    'tecnológico': 'tecnológico',
    'prático': 'prático',
    'automático': 'automático',
    'electrónico': 'eletrónico',
    'electrónicos': 'eletrónicos',
    'electrónica': 'eletrónica',

    // Consistency in Portuguese variants
    'eletrônico': 'eletrónico',
    'eletrônicos': 'eletrónicos',
    'eletrônica': 'eletrónica',

    // Common word corrections
    'optimizar': 'otimizar',
    'optimizado': 'otimizado',
    'optimização': 'otimização'
  };

  let corrected = text;

  // Apply corrections
  Object.entries(corrections).forEach(([wrong, right]) => {
    const regex = new RegExp(`\\b${wrong}\\b`, 'gi');
    corrected = corrected.replace(regex, right);
  });

  return corrected;
}

function correctGrammarAndFormatting(text: string): string {
  if (!text) return '';

  let corrected = text;

  // Fix spacing issues
  corrected = corrected.replace(/\s+/g, ' '); // Multiple spaces to single space
  corrected = corrected.replace(/\s+([.,;:!?])/g, '$1'); // Remove space before punctuation
  corrected = corrected.replace(/([.,;:!?])([a-zA-Z])/g, '$1 $2'); // Add space after punctuation

  // Fix capitalization after punctuation
  corrected = corrected.replace(/([.!?])\s+([a-z])/g, (match, punct, letter) => {
    return punct + ' ' + letter.toUpperCase();
  });

  // Ensure proper sentence structure
  corrected = corrected.replace(/^\s*([a-z])/g, (match, letter) => {
    return letter.toUpperCase();
  });

  // Fix common grammar patterns
  corrected = corrected.replace(/\bde a\b/g, 'da');
  corrected = corrected.replace(/\bde o\b/g, 'do');
  corrected = corrected.replace(/\bde os\b/g, 'dos');
  corrected = corrected.replace(/\bde as\b/g, 'das');
  corrected = corrected.replace(/\bem o\b/g, 'no');
  corrected = corrected.replace(/\bem a\b/g, 'na');
  corrected = corrected.replace(/\bem os\b/g, 'nos');
  corrected = corrected.replace(/\bem as\b/g, 'nas');

  // Remove extra line breaks and normalize HTML formatting
  if (corrected.includes('<')) {
    corrected = corrected.replace(/>\s+</g, '><'); // Remove spaces between tags
    corrected = corrected.replace(/\n\s*\n\s*\n/g, '\n\n'); // Max two line breaks
  }

  return corrected.trim();
}

// Enhanced mock function with natural, engaging content and automatic error correction
function generateMockSeoContent(productInfo: ProductInfo): SeoContent {
  const productName = standardizeTitle(productInfo.name || 'Produto Exclusivo');
  const category = correctSpelling(productInfo.category || 'categoria premium');
  const features = productInfo.features?.filter(f => f.trim()).map(f => correctSpelling(f.trim())) || [];
  const targetAudience = correctSpelling(productInfo.targetAudience || 'quem procura qualidade e estilo');
  const additionalInfo = correctSpelling(productInfo.additionalInfo || '');

  // Generate natural, flowing descriptions
  const naturalDescriptions = generateNaturalDescriptions(productName, category, features, targetAudience, additionalInfo);

  // Apply automatic error correction to all generated content
  return {
    wooCommerceMainDescription: correctGrammarAndFormatting(naturalDescriptions.mainDescription),
    wooCommerceShortDescription: correctGrammarAndFormatting(naturalDescriptions.shortDescription),
    shortDescription: correctGrammarAndFormatting(naturalDescriptions.seoDescription),
    slug: generateSlug(productName)
  };
}

// Generate natural, human-like product descriptions following the new structure
function generateNaturalDescriptions(productName: string, category: string, features: string[], targetAudience: string, additionalInfo: string) {
  // Create contextual opening paragraphs that highlight main benefit
  const contextualOpenings = [
    `Perfeito para ${targetAudience}, o ${productName} foi pensado para quem precisa de funcionalidade e praticidade no dia a dia.`,
    `Ideal para ${targetAudience}, o ${productName} combina design inteligente com desempenho excecional.`,
    `Desenvolvido especialmente para ${targetAudience}, o ${productName} oferece a solução que procurava.`,
    `Pensado para ${targetAudience}, o ${productName} destaca-se pela sua versatilidade e eficiência.`,
    `Criado para ${targetAudience}, o ${productName} representa a escolha certa para quem valoriza qualidade.`
  ];

  // Create practical usage scenarios
  const usageScenarios = [
    `Ideal para uso diário, este produto adapta-se perfeitamente à sua rotina e necessidades específicas.`,
    `Perfeito para quem procura uma solução prática e eficiente no seu dia a dia.`,
    `Adequado para diversas situações, oferece a flexibilidade que precisa em qualquer momento.`,
    `Pensado para integrar-se naturalmente na sua vida, simplificando tarefas e melhorando a experiência.`,
    `Concebido para acompanhar o seu ritmo, proporcionando comodidade e funcionalidade em todas as ocasiões.`
  ];

  // Create natural call-to-action endings
  const naturalClosings = [
    `Descubra uma nova forma de ${getActionForCategory(category)} com praticidade e confiança.`,
    `Experimente a diferença que um produto bem pensado pode fazer no seu dia a dia.`,
    `Adicione esta solução à sua rotina e sinta a melhoria na qualidade e eficiência.`,
    `Invista numa escolha que vai acompanhar as suas necessidades a longo prazo.`,
    `Transforme a sua experiência com um produto que realmente faz a diferença.`
  ];

  // Randomly select elements for variety
  const opening = contextualOpenings[Math.floor(Math.random() * contextualOpenings.length)];
  const usage = usageScenarios[Math.floor(Math.random() * usageScenarios.length)];
  const closing = naturalClosings[Math.floor(Math.random() * naturalClosings.length)];

  // Build main description following the new 4-paragraph structure
  let mainDescription = `<div class="product-description">
    <h2>${productName}</h2>

    <p class="intro-paragraph">${opening} ${getDesignContext(category)} adapta-se tanto a ambientes profissionais como a ocasiões mais casuais.</p>

    <p class="technical-paragraph">${buildTechnicalDescription(features, additionalInfo)}</p>

    <p class="usage-paragraph">${usage} ${getUsageContext(category, targetAudience)}</p>

    <p class="closing-paragraph">${closing} ${getClosingAction(productName, category)}</p>
  </div>`;

  // Generate short description (for WooCommerce excerpt) - wrapped in HTML for proper formatting
  const shortDescription = `<p>${productName} ${category} de qualidade superior. ${features.length > 0 ? `Com ${features.slice(0, 2).join(' e ')}, ` : ''}ideal para ${targetAudience}. Qualidade garantida e entrega rápida.</p>`;

  // Generate SEO description (140-160 characters)
  const seoDescription = generateOptimalSeoDescription(productName, category, features, targetAudience);

  return {
    mainDescription,
    shortDescription,
    seoDescription
  };
}

// Helper function to get action verb based on category
function getActionForCategory(category: string): string {
  const categoryActions: { [key: string]: string } = {
    'eletrónica': 'utilizar tecnologia',
    'mobiliário': 'organizar o seu espaço',
    'roupa': 'vestir-se',
    'casa': 'cuidar da sua casa',
    'jardim': 'cuidar do seu jardim',
    'desporto': 'praticar desporto',
    'beleza': 'cuidar de si',
    'automóvel': 'cuidar do seu veículo'
  };

  return categoryActions[category.toLowerCase()] || 'melhorar a sua experiência';
}

// Helper function to get design context based on category
function getDesignContext(category: string): string {
  const designContexts: { [key: string]: string } = {
    'eletrónica': 'Com um design moderno e acabamento tecnológico,',
    'mobiliário': 'Com linhas elegantes e acabamento cuidado,',
    'roupa': 'Com um corte moderno e tecidos de qualidade,',
    'casa': 'Com design funcional e estética cuidada,',
    'jardim': 'Com design prático e materiais resistentes,',
    'desporto': 'Com design ergonómico e materiais técnicos,',
    'beleza': 'Com fórmula cuidada e apresentação elegante,',
    'automóvel': 'Com design robusto e acabamento profissional,'
  };

  return designContexts[category.toLowerCase()] || 'Com design cuidado e funcional,';
}

// Helper function to build technical description from features
function buildTechnicalDescription(features: string[], additionalInfo: string): string {
  let techDesc = '';

  if (features.length > 0) {
    const enhancedFeatures = features.map(feature => enhanceFeatureForTechnical(feature.trim()));
    techDesc = enhancedFeatures.join(', ') + '.';
  }

  if (additionalInfo) {
    techDesc += ` ${additionalInfo}`;
  }

  if (!techDesc) {
    techDesc = 'Fabricado com materiais de qualidade e atenção aos detalhes, oferece durabilidade e funcionalidade excepcionais.';
  }

  return techDesc;
}

// Enhanced feature description for technical paragraph
function enhanceFeatureForTechnical(feature: string): string {
  const technicalEnhancements: { [key: string]: string } = {
    'resistente': 'construção resistente que garante durabilidade',
    'leve': 'peso reduzido para facilidade de manuseamento',
    'impermeável': 'proteção total contra água e humidade',
    'portátil': 'design compacto para máxima portabilidade',
    'ergonómico': 'forma ergonómica para conforto prolongado',
    'rápido': 'funcionamento rápido e eficiente',
    'seguro': 'sistemas de segurança integrados',
    'económico': 'consumo eficiente de energia',
    'moderno': 'tecnologia atual e design contemporâneo',
    'premium': 'materiais premium e acabamentos superiores'
  };

  for (const [key, enhancement] of Object.entries(technicalEnhancements)) {
    if (feature.toLowerCase().includes(key)) {
      return enhancement;
    }
  }

  return `${feature} de alta qualidade`;
}

// Helper function to get usage context
function getUsageContext(category: string, targetAudience: string): string {
  const usageContexts: { [key: string]: string } = {
    'eletrónica': 'Simplifica tarefas tecnológicas e melhora a produtividade no trabalho ou em casa.',
    'mobiliário': 'Transforma o ambiente e otimiza o aproveitamento do espaço disponível.',
    'roupa': 'Complementa o guarda-roupa com versatilidade e conforto para diversas ocasiões.',
    'casa': 'Melhora a funcionalidade doméstica e contribui para um ambiente mais organizado.',
    'jardim': 'Facilita o cuidado das plantas e melhora a aparência do espaço exterior.',
    'desporto': 'Apoia a prática desportiva e contribui para melhores resultados.',
    'beleza': 'Integra-se na rotina de cuidados pessoais com resultados visíveis.',
    'automóvel': 'Melhora a experiência de condução e o cuidado do veículo.'
  };

  return usageContexts[category.toLowerCase()] || 'Integra-se perfeitamente na rotina diária, oferecendo praticidade e eficiência.';
}

// Helper function to get closing action
function getClosingAction(productName: string, category: string): string {
  const closingActions: { [key: string]: string } = {
    'eletrónica': `Adicione o ${productName} ao seu setup tecnológico e experimente a diferença na eficiência.`,
    'mobiliário': `Integre o ${productName} no seu espaço e transforme o ambiente.`,
    'roupa': `Adicione o ${productName} ao seu guarda-roupa e renove o seu estilo.`,
    'casa': `Inclua o ${productName} na sua casa e sinta a melhoria no dia a dia.`,
    'jardim': `Adicione o ${productName} aos seus cuidados de jardim e veja os resultados.`,
    'desporto': `Integre o ${productName} no seu treino e potencialize os resultados.`,
    'beleza': `Inclua o ${productName} na sua rotina e descubra os benefícios.`,
    'automóvel': `Adicione o ${productName} ao cuidado do seu veículo e sinta a diferença.`
  };

  return closingActions[category.toLowerCase()] || `Experimente o ${productName} e descubra como pode melhorar a sua experiência.`;
}

// Helper function to enhance feature descriptions
function enhanceFeatureDescription(feature: string): string {
  const enhancements = {
    // Common feature patterns and their enhanced versions
    'resistente': 'Construção resistente e durável para uso prolongado',
    'leve': 'Design leve e ergonómico para máximo conforto',
    'impermeável': 'Proteção impermeável total contra água e humidade',
    'portátil': 'Portabilidade excepcional para uso em qualquer lugar',
    'fácil': 'Facilidade de uso intuitiva e prática',
    'rápido': 'Desempenho rápido e eficiente',
    'seguro': 'Segurança garantida com tecnologia avançada',
    'económico': 'Solução económica com excelente relação qualidade-preço',
    'moderno': 'Design moderno e contemporâneo',
    'premium': 'Qualidade premium com acabamentos superiores'
  };

  // Check if feature matches any enhancement pattern
  for (const [key, enhancement] of Object.entries(enhancements)) {
    if (feature.toLowerCase().includes(key)) {
      return enhancement;
    }
  }

  // If no specific enhancement, add descriptive language
  return `${feature} - característica que garante qualidade e funcionalidade superior`;
}

// Generate SEO-optimized description (140-160 characters)
function generateOptimalSeoDescription(productName: string, category: string, features: string[], targetAudience: string): string {
  const baseDescription = `${productName} ${category}`;
  let seoDesc = baseDescription;

  // Add key features if space allows
  if (features.length > 0) {
    const keyFeature = features[0].toLowerCase();
    seoDesc += ` com ${keyFeature}`;
  }

  // Add target audience
  seoDesc += ` para ${targetAudience}`;

  // Add compelling ending
  const endings = [
    '. Qualidade premium garantida',
    '. Entrega rápida em Portugal',
    '. Melhor preço e qualidade',
    '. Compre já com garantia',
    '. Produto de confiança'
  ];

  // Choose ending that fits within 160 characters
  for (const ending of endings) {
    if ((seoDesc + ending).length <= 160 && (seoDesc + ending).length >= 140) {
      return seoDesc + ending;
    }
  }

  // If still too short, add more descriptive content
  if (seoDesc.length < 140) {
    seoDesc += '. Produto de qualidade superior com garantia de satisfação total';
  }

  // Ensure it's within limits
  if (seoDesc.length > 160) {
    seoDesc = seoDesc.substring(0, 157) + '...';
  }

  return seoDesc;
}

function generateMockImprovedContent(currentDescription: string, productName?: string): SeoContent {
  const name = standardizeTitle(productName || 'Produto Otimizado');
  const cleanDesc = correctGrammarAndFormatting(currentDescription.replace(/<[^>]*>/g, '').trim());

  // Extract key information from current description
  const words = cleanDesc.split(' ').filter(word => word.length > 3);
  const keyWords = words.slice(0, 5).join(' ');

  // Generate improved content with natural flow
  const improvements = [
    'design renovado e mais atrativo',
    'funcionalidades aprimoradas para melhor desempenho',
    'materiais de qualidade superior',
    'tecnologia mais avançada',
    'maior durabilidade e resistência',
    'melhor experiência do utilizador',
    'características otimizadas'
  ];

  const selectedImprovements = improvements.slice(0, 3);

  const improvedMainDescription = `<div class="improved-product">
    <h2>🚀 ${name} - Versão Melhorada</h2>

    <div class="original-content">
      <h3>📝 Descrição Original</h3>
      <p>${cleanDesc}</p>
    </div>

    <div class="improvements">
      <h3>⚡ Melhorias Implementadas</h3>
      <p>Esta versão otimizada do <strong>${name}</strong> incorpora várias melhorias significativas que elevam a qualidade e funcionalidade do produto:</p>

      <ul class="improvement-list">
        ${selectedImprovements.map(improvement => `<li>🔧 ${improvement.charAt(0).toUpperCase() + improvement.slice(1)}</li>`).join('')}
      </ul>

      <p>Estas melhorias foram desenvolvidas com base no feedback dos utilizadores e nas mais recentes inovações tecnológicas, garantindo um produto que supera as expectativas.</p>
    </div>

    <div class="benefits">
      <h3>🎯 Vantagens da Versão Melhorada</h3>
      <p>Ao escolher esta versão otimizada, beneficia de um produto que combina a confiabilidade comprovada com as mais recentes inovações. Uma escolha inteligente para quem procura o melhor em qualidade e desempenho.</p>

      <div class="guarantee-section">
        <p><strong>✨ Qualidade Aprimorada:</strong> Todas as melhorias foram testadas e validadas.</p>
        <p><strong>🔄 Compatibilidade:</strong> Mantém todas as funcionalidades da versão anterior.</p>
        <p><strong>📈 Performance:</strong> Desempenho superior em todos os aspetos.</p>
      </div>
    </div>
  </div>`;

  const improvedShortDescription = `${name} - versão melhorada com ${selectedImprovements[0]} e ${selectedImprovements[1]}. Qualidade superior garantida.`;

  const improvedSeoDescription = generateOptimalSeoDescription(
    name,
    'versão melhorada',
    ['otimizado', 'melhorado'],
    'quem procura qualidade superior'
  );

  return {
    wooCommerceMainDescription: correctGrammarAndFormatting(improvedMainDescription),
    wooCommerceShortDescription: correctGrammarAndFormatting(improvedShortDescription),
    shortDescription: correctGrammarAndFormatting(improvedSeoDescription),
    slug: generateSlug(name)
  };
}

export async function POST(request: Request) {
  // Check if we should use mock mode
  if (mockMode) {
    try {
      const body = await request.json();
      let seoContent: SeoContent;

      if (body.action === 'generate') {
        seoContent = generateMockSeoContent(body.productInfo);
      } else if (body.action === 'improve') {
        seoContent = generateMockImprovedContent(body.currentDescription, body.productInfo?.name);
      } else {
        return NextResponse.json({ error: 'Ação inválida. Use "generate" ou "improve".' }, { status: 400 });
      }

      // Simulate API delay for realistic testing
      await new Promise(resolve => setTimeout(resolve, 1000));

      return NextResponse.json({ seoContent });
    } catch (error) {
      console.error('Erro no modo mock:', error);
      return NextResponse.json({ error: 'Erro no modo de teste.' }, { status: 500 });
    }
  }

  if (!openai) {
    return NextResponse.json(
      { error: 'A API da OpenAI não está configurada. Por favor, defina a variável de ambiente OPENAI_API_KEY ou ative o modo mock.' },
      { status: 500 }
    );
  }

  try {
    const body = await request.json();
    let seoContent: SeoContent;

    if (body.action === 'generate') {
      seoContent = await generateSeoContent(body.productInfo);
    } else if (body.action === 'improve') {
      seoContent = await improveSeoContent(body.currentDescription, body.productInfo?.name);
    } else {
      return NextResponse.json({ error: 'Ação inválida. Use "generate" ou "improve".' }, { status: 400 });
    }

    return NextResponse.json({ seoContent });

  } catch (error) {
    console.error('Erro na API de descrição de produto:', error);

    // Handle specific OpenAI API errors
    if (error && typeof error === 'object' && 'status' in error) {
      const openaiError = error as any;

      switch (openaiError.status) {
        case 429:
          return NextResponse.json({
            error: 'Quota da API OpenAI excedida. Por favor, verifique o seu plano e detalhes de faturação.'
          }, { status: 429 });

        case 401:
          return NextResponse.json({
            error: 'Chave da API OpenAI inválida. Por favor, verifique a configuração.'
          }, { status: 401 });

        case 400:
          return NextResponse.json({
            error: 'Pedido inválido enviado para a API OpenAI. Por favor, tente novamente.'
          }, { status: 400 });

        case 503:
          return NextResponse.json({
            error: 'Serviço OpenAI temporariamente indisponível. Tente novamente em alguns minutos.'
          }, { status: 503 });

        default:
          return NextResponse.json({
            error: `Erro da API OpenAI (${openaiError.status}): ${openaiError.message || 'Erro desconhecido'}`
          }, { status: openaiError.status || 500 });
      }
    }

    // Handle other types of errors
    if (error instanceof Error) {
      if (error.message.includes('API da OpenAI não configurada')) {
        return NextResponse.json({
          error: 'API da OpenAI não está configurada. Por favor, defina a variável de ambiente OPENAI_API_KEY.'
        }, { status: 500 });
      }

      if (error.message.includes('JSON válido')) {
        return NextResponse.json({
          error: 'Resposta inválida da API OpenAI. Tente novamente.'
        }, { status: 500 });
      }

      return NextResponse.json({
        error: `Erro: ${error.message}`
      }, { status: 500 });
    }

    // Fallback for unknown errors
    return NextResponse.json({
      error: 'Falha ao processar o pedido. Tente novamente.'
    }, { status: 500 });
  }
}

async function generateSeoContent(productInfo: ProductInfo): Promise<SeoContent> {
  if (!openai) throw new Error('API da OpenAI não configurada');

  const prompt = `
    Analise as seguintes informações de um produto para uma loja WooCommerce em Portugal:
    - Nome do Produto: ${productInfo.name}
    ${productInfo.category ? `- Categoria: ${productInfo.category}` : ''}
    ${productInfo.features && productInfo.features.length > 0 ? `- Características: ${productInfo.features.join(', ')}` : ''}
    ${productInfo.keywords && productInfo.keywords.length > 0 ? `- Palavras-chave para SEO: ${productInfo.keywords.join(', ')}` : ''}
    ${productInfo.targetAudience ? `- Público-alvo: ${productInfo.targetAudience}` : ''}
    ${productInfo.additionalInfo ? `- Informações Adicionais: ${productInfo.additionalInfo}` : ''}

    Com base nisso, gere o seguinte conteúdo em português de Portugal, seguindo estritamente as regras para cada campo. A resposta DEVE ser um objeto JSON válido.

    **IMPORTANTE:** A curta descrição deve ser uma versão resumida e específica da descrição principal, não um texto genérico. Extraia as características mais importantes da descrição completa.

    **CRÍTICO - LIMITE SEO:** A descrição SEO (shortDescription) DEVE ter MÁXIMO 160 caracteres. CONTE OS CARACTERES. Se passar de 160, REESCREVA até ficar dentro do limite. Seja específico sobre o produto, não genérico.

    1.  **wooCommerceMainDescription (Descrição WooCommerce):**
        -   **Objetivo:** Ser a descrição completa do produto no WooCommerce. Deve ser rica em detalhes e otimizada para conversão.
        -   **Estilo:** Use formatação HTML estruturada para melhor apresentação visual.
        -   **Tom:** Abrangente, informativo e persuasivo.
        -   **Formato OBRIGATÓRIO:** Use esta estrutura HTML:
            1. Parágrafo de abertura com tag p
            2. Título "Características principais:" em strong
            3. Características em parágrafos separados (sem listas ul/li)
            4. Parágrafo de fecho com tag p
        -   **Formatação das características:** Cada característica deve ser um parágrafo separado, claro e específico, destacando um benefício único. Use linguagem persuasiva e técnica quando apropriado.
        -   **Exemplo de estrutura:**
            <p>Parágrafo introdutório...</p>
            <strong>Características principais:</strong>
            <p><strong>Nome da característica:</strong> Descrição detalhada do benefício</p>
            <p><strong>Segunda característica:</strong> Explicação clara e persuasiva</p>
            <p><strong>Terceira característica:</strong> Mais detalhes específicos</p>
            <p>Parágrafo de fecho...</p>
        -   **Tamanho:** Entre 200-400 palavras para ser completa mas não excessiva.

    2.  **wooCommerceShortDescription (Curta Descrição WooCommerce):**
        -   **Objetivo:** Ser uma versão resumida e específica da descrição principal em formato de texto corrido.
        -   **Estilo:** Um parágrafo conciso que sintetiza os pontos mais importantes da descrição completa.
        -   **Tom:** Direto, específico e focado nos benefícios únicos deste produto em particular.
        -   **Formato:** Texto corrido em parágrafo HTML simples usando tag <p>, SEM listas.
        -   **Conteúdo:** Deve resumir as principais características e benefícios mencionados na descrição completa.
        -   **Importante:** NÃO use frases genéricas. Seja específico sobre este produto e suas características únicas.
        -   **Tamanho:** 2-3 frases que capturem a essência do produto e seus principais benefícios.

    3.  **shortDescription (Descrição SEO):**
        -   **Objetivo:** Meta description inteligente e específica que identifique exatamente o que é o produto e seus benefícios únicos.
        -   **REGRA ABSOLUTA:** MÁXIMO 160 caracteres (incluindo espaços). Conte cada caractere. Se passar de 160, REESCREVA até ficar dentro do limite.
        -   **Conteúdo OBRIGATÓRIO:** Deve incluir:
            1. O QUE é o produto (tipo específico, não genérico)
            2. PRINCIPAL característica/benefício único
            3. PARA QUEM é destinado (se relevante)
        -   **Estilo:** Específico, direto, sem palavras vazias como "alta qualidade", "excelente", "premium" (a menos que seja realmente diferencial)
        -   **Exemplos de RUIM (genéricos):**
            * "Produto de alta qualidade com excelente design e materiais premium"
            * "Artigo moderno e funcional para uso diário"
        -   **Exemplos de BOM (específicos):**
            * "Sapatos de couro italiano com palmilha gel anti-impacto para profissionais" (79 chars)
            * "Smartphone Android 12GB RAM, câmara 108MP, bateria 5000mAh" (60 chars)
            * "Mesa escritório madeira maciça 120x60cm com gavetas organizadoras" (67 chars)
        -   **FÓRMULA:** [TIPO PRODUTO] + [MATERIAL/TECNOLOGIA] + [BENEFÍCIO PRINCIPAL] + [PÚBLICO-ALVO se relevante]
        -   **VERIFICAÇÃO FINAL:** Conte os caracteres. Se > 160, corte palavras até ficar ≤ 160.

    4.  **slug (Slug):**
        -   **Objetivo:** Criar um URL amigável para SEO.
        -   **Estilo:** Use o nome do produto como base. Converta para minúsculas, substitua espaços por hífens e remova "stop words" (ex: de, para, o, a) e caracteres especiais.
        -   **Exemplo:** Se o nome for "Sapatos de Couro para Homem", o slug deve ser "sapatos-couro-homem".

    Responda APENAS com o objeto JSON, formatado da seguinte maneira:
    {
      "wooCommerceMainDescription": "(texto aqui)",
      "wooCommerceShortDescription": "(texto aqui)",
      "shortDescription": "(texto aqui)",
      "slug": "(slug aqui)"
    }
  `;

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em SEO e copywriting para e-commerce, focado em criar conteúdo otimizado para produtos WooCommerce em Portugal. Sua tarefa é gerar um conjunto de conteúdos (descrição WooCommerce, curta descrição WooCommerce, descrição SEO e slug) a partir de informações do produto. A resposta deve ser sempre um objeto JSON válido."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.6,
      max_tokens: 1200,
      response_format: { type: "json_object" },
    });

    const content = response.choices[0].message.content;
    if (!content) throw new Error("A resposta da API está vazia.");

    try {
      const parsedContent: SeoContent = JSON.parse(content);

      // Garante que o slug está bem formatado, mesmo que a IA falhe
      parsedContent.slug = generateSlug(parsedContent.slug || productInfo.name);

      // Validar limites de SEO
      const validation = validateSeoLimits(parsedContent);
      if (!validation.isValid) {
        console.warn("Limites de SEO não respeitados:", validation.errors);

        // Tentar corrigir a meta description se estiver fora dos limites
        if (parsedContent.shortDescription.length > 160) {
          // Se muito longa, truncar de forma inteligente
          let truncated = parsedContent.shortDescription.substring(0, 157);

          // Tentar cortar na última palavra completa
          const lastSpace = truncated.lastIndexOf(' ');
          if (lastSpace > 140) {
            truncated = truncated.substring(0, lastSpace);
          }

          // Adicionar reticências se necessário
          if (truncated.length < parsedContent.shortDescription.length) {
            truncated += "...";
          }

          // Garantir que não excede 160 caracteres
          if (truncated.length > 160) {
            truncated = truncated.substring(0, 157) + "...";
          }

          parsedContent.shortDescription = truncated;
          console.log(`Meta description corrigida: ${parsedContent.shortDescription.length} caracteres`);
        }
      }

      return parsedContent;
    } catch (e) {
      console.error("Falha ao analisar JSON da API:", content);
      throw new Error("A resposta da API não é um JSON válido.");
    }
  } catch (error) {
    // Re-throw OpenAI API errors to be handled by the main catch block
    throw error;
  }
}

async function improveSeoContent(currentDescription: string, productName?: string): Promise<SeoContent> {
  if (!openai) throw new Error('API da OpenAI não configurada');

  const prompt = `
    Analise a seguinte descrição de produto (e o seu nome, se disponível) de uma loja WooCommerce em Portugal:
    - Nome do Produto: ${productName || 'Não fornecido'}
    - Descrição Atual: "${currentDescription}"

    Com base nisso, reescreva e otimize o conteúdo, gerando os seguintes elementos em português de Portugal. A resposta DEVE ser um objeto JSON válido.

    **IMPORTANTE:** A curta descrição deve ser uma versão resumida e específica da descrição principal, não um texto genérico. Extraia as características mais importantes da descrição completa.

    **CRÍTICO - LIMITE SEO:** A descrição SEO (shortDescription) DEVE ter MÁXIMO 160 caracteres. CONTE OS CARACTERES. Se passar de 160, REESCREVA até ficar dentro do limite. Seja específico sobre o produto, não genérico.

    1.  **wooCommerceMainDescription (Descrição WooCommerce):**
        -   **Objetivo:** Ser a descrição completa do produto no WooCommerce. Deve ser rica em detalhes e otimizada para conversão.
        -   **Estilo:** Use formatação HTML estruturada para melhor apresentação visual.
        -   **Tom:** Abrangente, informativo e persuasivo.
        -   **Formato OBRIGATÓRIO:** Use esta estrutura HTML:
            1. Parágrafo de abertura com tag p
            2. Título "Características principais:" em strong
            3. Características em parágrafos separados (sem listas ul/li)
            4. Parágrafo de fecho com tag p
        -   **Formatação das características:** Cada característica deve ser um parágrafo separado, claro e específico, destacando um benefício único. Use linguagem persuasiva e técnica quando apropriado.
        -   **Exemplo de estrutura:**
            <p>Parágrafo introdutório...</p>
            <strong>Características principais:</strong>
            <p><strong>Nome da característica:</strong> Descrição detalhada do benefício</p>
            <p><strong>Segunda característica:</strong> Explicação clara e persuasiva</p>
            <p><strong>Terceira característica:</strong> Mais detalhes específicos</p>
            <p>Parágrafo de fecho...</p>
        -   **Tamanho:** Entre 200-400 palavras para ser completa mas não excessiva.

    2.  **wooCommerceShortDescription (Curta Descrição WooCommerce):**
        -   **Objetivo:** Ser uma versão resumida e específica da descrição principal, destacando os pontos mais importantes do produto específico.
        -   **Estilo:** Deve ser uma síntese da descrição principal, mantendo as características mais relevantes e específicas do produto em questão.
        -   **Tom:** Direto, específico e focado nos benefícios únicos deste produto em particular.
        -   **Formato:** Use parágrafos separados (sem listas ul/li) com 3-4 características principais extraídas da descrição completa.
        -   **Formatação obrigatória:** <p><strong>Característica específica 1:</strong> Benefício concreto</p><p><strong>Característica específica 2:</strong> Benefício concreto</p><p><strong>Característica específica 3:</strong> Benefício concreto</p>
        -   **Importante:** NÃO use frases genéricas. Seja específico sobre este produto em particular.
        -   **Tamanho:** Máximo 3-4 itens, cada um com características específicas do produto.

    3.  **shortDescription (Descrição SEO):**
        -   **Objetivo:** Meta description inteligente e específica que identifique exatamente o que é o produto e seus benefícios únicos.
        -   **REGRA ABSOLUTA:** MÁXIMO 160 caracteres (incluindo espaços). Conte cada caractere. Se passar de 160, REESCREVA até ficar dentro do limite.
        -   **Conteúdo OBRIGATÓRIO:** Deve incluir:
            1. O QUE é o produto (tipo específico, não genérico)
            2. PRINCIPAL característica/benefício único
            3. PARA QUEM é destinado (se relevante)
        -   **Estilo:** Específico, direto, sem palavras vazias como "alta qualidade", "excelente", "premium" (a menos que seja realmente diferencial)
        -   **Exemplos de RUIM (genéricos):**
            * "Produto de alta qualidade com excelente design e materiais premium"
            * "Artigo moderno e funcional para uso diário"
        -   **Exemplos de BOM (específicos):**
            * "Sapatos de couro italiano com palmilha gel anti-impacto para profissionais" (79 chars)
            * "Smartphone Android 12GB RAM, câmara 108MP, bateria 5000mAh" (60 chars)
            * "Mesa escritório madeira maciça 120x60cm com gavetas organizadoras" (67 chars)
        -   **FÓRMULA:** [TIPO PRODUTO] + [MATERIAL/TECNOLOGIA] + [BENEFÍCIO PRINCIPAL] + [PÚBLICO-ALVO se relevante]
        -   **VERIFICAÇÃO FINAL:** Conte os caracteres. Se > 160, corte palavras até ficar ≤ 160.

    4.  **slug (Slug):**
        -   **Objetivo:** Gerar um slug de URL otimizado a partir do nome do produto ou, se não disponível, a partir da descrição.
        -   **Estilo:** Minúsculas, hífens para espaços, sem "stop words".

    Responda APENAS com o objeto JSON, formatado da seguinte maneira:
    {
      "wooCommerceMainDescription": "(texto aqui)",
      "wooCommerceShortDescription": "(texto aqui)",
      "shortDescription": "(texto aqui)",
      "slug": "(slug aqui)"
    }
  `;

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4-turbo",
      messages: [
        {
          role: "system",
          content: "Você é um especialista em SEO e copywriting para e-commerce, focado em otimizar conteúdo de produtos WooCommerce em Portugal. Sua tarefa é reescrever uma descrição existente para criar um conjunto completo de conteúdos (descrição WooCommerce, curta descrição WooCommerce, descrição SEO e slug). A resposta deve ser sempre um objeto JSON válido."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.6,
      max_tokens: 1200,
      response_format: { type: "json_object" },
    });

    const content = response.choices[0].message.content;
    if (!content) throw new Error("A resposta da API está vazia.");

    try {
      const parsedContent: SeoContent = JSON.parse(content);

      // Garante que o slug está bem formatado
      parsedContent.slug = generateSlug(parsedContent.slug || productName || currentDescription.substring(0, 50));

      // Validar limites de SEO
      const validation = validateSeoLimits(parsedContent);
      if (!validation.isValid) {
        console.warn("Limites de SEO não respeitados:", validation.errors);

        // Tentar corrigir a meta description se estiver fora dos limites
        if (parsedContent.shortDescription.length < 140) {
          // Se muito curta, expandir ligeiramente
          parsedContent.shortDescription = parsedContent.shortDescription + " Descubra mais detalhes.";
        } else if (parsedContent.shortDescription.length > 160) {
          // Se muito longa, truncar mantendo sentido
          parsedContent.shortDescription = parsedContent.shortDescription.substring(0, 157) + "...";
        }
      }

      return parsedContent;
    } catch (e) {
      console.error("Falha ao analisar JSON da API:", content);
      throw new Error("A resposta da API não é um JSON válido.");
    }
  } catch (error) {
    // Re-throw OpenAI API errors to be handled by the main catch block
    throw error;
  }
}
